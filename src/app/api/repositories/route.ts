import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/src/features/auth/lib/auth-config";
import connectDB from "@/src/lib/database/mongodb";
import { VCSInstallation, Repository } from "@/src/lib/database/models";

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Find user's VCS installations
    const installations = await VCSInstallation.find({
      userId: session.user.id
    }).select('_id');

    const installationIds = installations.map((inst: any) => inst._id.toString());

    // Get repositories with pagination and search
    const repositories = await Repository.find({
      vcsInstallationId: { $in: installationIds },
      name: { $regex: search, $options: 'i' }
    })
      .skip(skip)
      .limit(limit)
      .sort({ name: 1 });

    // Get total count for pagination
    const totalCount = await Repository.countDocuments({
      vcsInstallationId: { $in: installationIds },
      name: { $regex: search, $options: 'i' }
    });

    return NextResponse.json({
      repositories,
      pagination: {
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
        page,
        limit
      }
    });
  } catch (error) {
    console.error("Error fetching repositories:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}